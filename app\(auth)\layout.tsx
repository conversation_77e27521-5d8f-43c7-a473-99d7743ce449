import Image from "next/image";
import React from "react";

function Layout({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex min-h-screen">
      <section className="bg-brand hidden w-1/2 flex-col items-center justify-center p-10 lg:flex xl:w-2/5">
        <div className="flex max-h-[800px] max-w-[430px] flex-col justify-center space-y-12">
          <Image
            src={"assets/icons/logo-full.svg"}
            alt=""
            width={224}
            height={82}
            className="h-auto"
          />
          <div className="space-y-5 text-white">
            <h1 className="h1">Manage your files the best way</h1>
            <p className="body-1">
              Awesome, we&apos;ve created the perfect place for you to store all
              your documents.
            </p>
          </div>
        </div>
        <Image
          src={"/assets/icons/illustration.svg"}
          alt=""
          width={342}
          height={342}
          className="h-auto transition-all hover:scale-105 hover:rotate-2"
        />
      </section>

      <section className="flex flex-1 flex-col items-center justify-center p-10">
        <div className="mb-16 lg:hidden">
          <Image
            src="/assets/icons/logo-full-brand.svg"
            alt="logo"
            width={224}
            height={82}
            className="h-auto w-[200px] lg:w-[250px]"
          />
        </div>
        {children}
      </section>
    </div>
  );
}

export default Layout;
