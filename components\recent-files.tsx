import Image from "next/image";
import { FileInfo } from "@/app/types";
import { getFileIcon, getFileColor } from "@/lib/utils";

const RecentFiles = ({ file }: { file: FileInfo }) => {
  const icon = getFileIcon({ type: file.type });
  return (
    <div className="mb-4 flex items-center justify-between">
      <div className="flex items-center justify-between gap-4">
        <div className={`w-fit rounded-full p-2 ${getFileColor(file.type)}`}>
          <Image src={icon} alt="file-icon" width={20} height={20} />
        </div>
        <div>
          {" "}
          <p className="font-bold">{file.name.slice(0, 40)}</p>
          <span className="text-muted-foreground text-sm">
            {file.createdAt.toString()}
          </span>
        </div>
      </div>
      {/* <OptionsMenu name={file.path} onDelete={onDelete} /> */}
    </div>
  );
};

export default RecentFiles;
