import { AppSidebar } from "@/components/app-sidebar";
import NavBar from "@/components/NavBar";
import { SidebarProvider } from "@/components/ui/sidebar";
import React from "react";
import { Toaster } from "@/components/ui/sonner";

const layout = ({ children }: Readonly<{ children: React.ReactNode }>) => {
  return (
    <SidebarProvider>
      <main className="flex h-screen w-screen">
        <AppSidebar />
        <div className="w-screen">
          <NavBar />
          <div className="main-content mr-0">{children}</div>
          <Toaster />
        </div>
      </main>
    </SidebarProvider>
  );
};

export default layout;
