import { fileType } from "@/app/types";
import Image from "next/image";
import { getFileIcon } from "@/lib/utils";
import MaskedDiv from "@/components/ui/masked-div";
import { FileInfo } from "@/app/types";
import { getFileSize } from "@/lib/utils";

const DashboardCard = ({
  type,
  files,
}: {
  type: fileType;
  files: FileInfo[];
}) => {
  const fileIcon = getFileIcon({ type });
  const fileSize = files
    .filter((file) => file.type === type)
    .reduce((acc, file) => acc + file.size, 0);
  const size = getFileSize(fileSize);
  console.log("fileSize", fileSize);
  return (
    <div className="relative">
      <div className="absolute top-[-20] right-[-13] w-fit rounded-full p-3.5">
        <Image src={fileIcon} alt="file-icon" width={40} height={40} />
      </div>
      <MaskedDiv maskType="type-1" backgroundColor="#ffffff" size={1}>
        <div className="p-2">
          <p className="mr-auto w-fit">{size}</p>
          <div className="pt-10 text-center">
            <p>{type}</p>
            <p>Last Updated</p>
            <span>2023-03-01</span>
          </div>
        </div>
      </MaskedDiv>
    </div>
  );
};

export default DashboardCard;
