"use client";
import { FileInfo, fileType } from "@/app/types";
import { useState } from "react";
import { listFiles } from "@/app/actions/userActions";
import { getFileIcon, getFileColor } from "@/lib/utils";
import Image from "next/image";
import OptionsMenu from "./options-menu";

const ListFiles = ({
  initialFiles,
  userId,
  fileType,
}: {
  initialFiles: FileInfo[];
  userId: string;
  fileType: fileType;
}) => {
  const [files, setFiles] = useState<FileInfo[]>(initialFiles);

  const refetchFiles = async () => {
    const updatedFiles = (await listFiles(userId)).filter(
      (file) => file.type === fileType,
    );
    setFiles(updatedFiles);
  };
  const icon = getFileIcon({ type: fileType });
  return (
    <div>
      <h2>Uploaded Files</h2>
      {files.length === 0 ? (
        <div>No files found</div>
      ) : (
        <div className="grid gap-4 sm:grid-cols-[repeat(auto-fit,minmax(200px,1fr))]">
          {files.map((file) => (
            <div key={file.id} className="rounded-lg bg-white p-5">
              <div
                className={`w-fit rounded-full p-2 ${getFileColor(file.type)}`}
              >
                <Image src={icon} alt="file-icon" width={20} height={20} />
              </div>
              <div className="ml-auto w-fit">
                <OptionsMenu name={file.path} onDelete={refetchFiles} />
                <p className="text-sm font-medium">2GB</p>
              </div>
              <p>{file.name}</p>
              <p className="text-muted-foreground text-sm">
                {file.createdAt.toString()}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
export default ListFiles;
