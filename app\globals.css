@import "tailwindcss";

@custom-variant dark (&:is(.dark *));
@plugin "tailwindcss-animate";

@theme {
  --color-background: oklch(0 0 1);
  --color-foreground: oklch(0.039 0 1);
  --color-card: oklch(0 0 1);
  --color-card-foreground: oklch(0.039 0 1);
  --color-popover: oklch(0 0 1);
  --color-popover-foreground: oklch(0.039 0 1);
  --color-primary: oklch(0.09 0 0.4);
  --color-primary-foreground: oklch(0.98 0 1);
  --color-secondary: oklch(0.961 0 0.5);
  --color-secondary-foreground: oklch(0.09 0 1);
  --color-muted: oklch(0.961 0 0.5);
  --color-muted-foreground: oklch(0.451 0 1);
  --color-accent: oklch(0.961 0 0.5);
  --color-accent-foreground: oklch(0.09 0 1);
  --color-destructive: oklch(0.602 0.84 0);
  --color-destructive-foreground: oklch(0.98 0 1);
  --color-border: oklch(0.898 0 0.5);
  --color-input: oklch(0.898 0 0.5);
  --color-ring: oklch(0.039 0 1);
  --color-chart-1: oklch(0.611 0.76 0.25);
  --color-chart-2: oklch(0.39 0.58 0.4);
  --color-chart-3: oklch(0.24 0.37 0.4);
  --color-chart-4: oklch(0.66 0.74 0.6);
  --color-chart-5: oklch(0.67 0.87 0.83);
  --color-radius: 0.5rem;

  --color-brand-100: oklch(66.94% 0.168 21.87);
  --color-brand: oklch(71.56% 0.167 20.9);
  --color-red: oklch(72.52% 0.17 22.24);
  --color-error: oklch(49.14% 0.202 29.23);
  --color-green: oklch(79.62% 0.139 172.43);
  --color-blue: oklch(75.38% 0.138 243.07);
  --color-pink: oklch(82.65% 0.137 320.36);
  --color-orange: oklch(80.47% 0.117 56.33);

  --color-light-100: oklch(36.33% 0.031 254.21);
  --color-light-200: oklch(75.88% 0.035 256.78);
  --color-light-300: oklch(96.9% 0.006 255.47);
  --color-light-400: oklch(96.68% 0.006 264.53);

  --color-dark-100: oklch(11.8% 0.019 274.6);
  --color-dark-200: oklch(20.16% 0.03 277.51);

  --animate-caret-blink: caret-blink 1.25s ease-out infinite;
}

/* Utility classes */
@utility center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
@utility h1 {
  font-size: 34px;
  line-height: 42px;
  font-weight: bold;
}
@utility h1 {
  font-size: 34px;
  line-height: 42px;
  font-weight: 700;
}

@utility h2 {
  font-size: 24px;
  line-height: 36px;
  font-weight: 700;
}

@utility h3 {
  font-size: 20px;
  line-height: 28px;
  font-weight: 600;
}

@utility h4 {
  font-size: 18px;
  line-height: 20px;
  font-weight: 500;
}

@utility h5 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
}

@utility subtitle-1 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
}

@utility subtitle-2 {
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
}

@utility body-1 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
}

@utility body-2 {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

@utility button {
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
}

@utility caption {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
}

@utility overline {
  font-size: 10px;
  line-height: 14px;
  font-weight: 400;
}

@utility container {
  margin-left: auto;
  margin-right: auto;
  max-width: 80rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

@utility primary-btn {
  background-color: var(--color-brand);
  transition: all 0.3s;
  border-radius: 9999px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
}

@utility primary-btn {
  background-color: var(--color-brand-100);
}

@utility flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@utility shad-no-focus {
  outline: none;
  --ring-offset-color: transparent;
  --ring-color: transparent;
}

@utility shad-no-focus {
  outline: none;
  box-shadow: none;
}

@utility shad-no-focus-visible {
  outline: none;
  box-shadow: none;
}

@utility shad-input {
  border: none;
  box-shadow: none;
  padding: 0;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

@utility shad-input {
  color: var(--light-200);
}

@utility shad-form-item {
  display: flex;
  height: 78px;
  flex-direction: column;
  justify-content: center;
  border-radius: 0.75rem;
  border: 1px solid var(--color-light-300);
  padding-left: 1rem;
  padding-right: 1rem;
  box-shadow: var(--drop-1);
}

@utility shad-form-label {
  color: var(--color-light-100);
  padding-top: 0.5rem;
  font-size: 14px;
  line-height: 20px;
  width: 100%;
}

@utility shad-form-message {
  color: var(--red);
  font-size: 14px;
  line-height: 20px;
  margin-left: 1rem;
}

@utility shad-alert-dialog {
  gap: 1rem;
  max-width: 95%;
  border-radius: 1.875rem;
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
  background-color: white;
  outline: none;
}

@utility shad-submit-btn {
  background-color: var(--color-brand);
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  transition: all 0.3s;
  border-radius: 9999px;
}

@utility shad-otp {
  width: 100%;
  display: flex;
  gap: 0.25rem;
  justify-content: space-between;
}

@utility shad-otp-slot {
  font-size: 40px;
  font-weight: 500;
  --ring-color: var(--color-brand);
  color: var(--color-brand-100);
  justify-content: center;
  display: flex;
  width: 3rem;
  height: 3rem;
  gap: 1.25rem;
}

@utility shad-sheet {
  padding-top: 0;
}

@utility shad-dialog-button {
  --ring-offset-shadow: 0 0 transparent;
  --ring-shadow: 0 0 transparent;
  outline: none;
}

@utility shad-dropdown-item {
  cursor: pointer;
}

@utility shad-dialog {
  border-radius: 1.625rem;
  width: 90%;
  max-width: 400px;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

@utility shad-chart-title {
  color: white;
}

@utility shad-select-item {
  cursor: pointer;
}

@utility nav-icon {
  width: 1.5rem;
  filter: invert(1);
  opacity: 0.25;
}

@utility nav-icon-active {
  filter: invert(0);
  opacity: 1;
}

@utility main-content {
  height: 100%;
  flex: 1;
  overflow: auto;
  background-color: var(--color-light-400);
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 1.75rem;
  padding-bottom: 1.75rem;
  margin-right: 1.75rem;
  border-radius: 1.875rem;
  margin-bottom: 1.75rem;
}

@utility dashboard-container {
  margin-left: auto;
  margin-right: auto;
  max-width: 80rem;
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 1.5rem;
}

@utility dashboard-summary-list {
  margin-top: 1.5rem;
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 1rem;
}

@utility dashboard-summary-card {
  position: relative;
  margin-top: 1.5rem;
  border-radius: 1.25rem;
  background-color: white;
  padding: 1.25rem;
  transition: all 0.3s;
}

@utility dashboard-summary-card {
  transform: scale(1.05);
}

@utility summary-type-icon {
  position: absolute;
  left: -0.75rem;
  top: -1.5rem;
  z-index: 10;
  width: 11.875rem;
  object-fit: contain;
}

@utility summary-type-size {
  position: relative;
  z-index: 20;
  width: 100%;
  text-align: right;
  font-size: 18px;
  line-height: 20px;
  font-weight: 500;
}

@utility summary-type-title {
  position: relative;
  z-index: 20;
  text-align: center;
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
}

@utility dashboard-recent-files {
  height: 100%;
  border-radius: 1.25rem;
  background-color: white;
  padding: 1.25rem;
}

@utility recent-file-details {
  display: flex;
  width: 100%;
  flex-direction: column;
}

@utility recent-file-name {
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  color: var(--color-light-100);
  max-width: 250px;
}

@utility recent-file-date {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
}

@utility empty-list {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  margin-top: 2.5rem;
  text-align: center;
  color: var(--color-light-200);
}

@utility page-container {
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  max-width: 80rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

@utility total-size-section {
  display: flex;
  margin-top: 0.5rem;
  flex-direction: column;
  justify-content: space-between;
}

@utility file-list {
  display: grid;
  width: 100%;
  gap: 1.5rem;
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

@utility sort-container {
  margin-top: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

@utility rename-input-field {
  font-size: 14px;
  line-height: 20px;
  height: 52px;
  width: 100%;
  border-radius: 9999px;
  border: 1px solid;
  padding-left: 1rem;
  padding-right: 1rem;
  outline: none;
}

@utility delete-confirmation {
  text-align: center;
  color: var(--light-100);
}

@utility delete-file-name {
  font-weight: 500;
  color: var(--color-brand-100);
}

@utility modal-cancel-button {
  height: 52px;
  flex: 1;
  border-radius: 9999px;
  background-color: white;
  color: var(--light-100);
}

@utility modal-cancel-button {
  background-color: transparent;
}

@utility modal-submit-button {
  height: 52px;
  width: 100%;
  flex: 1;
  background-color: var(--color-brand);
  transition: all 0.3s;
  border-radius: 9999px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
}

@utility modal-submit-button {
  background-color: var(--color-brand-100);
}

@utility file-details-thumbnail {
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-radius: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.4);
  background-color: rgba(255, 255, 255, 0.05);
  padding: 0.75rem;
}

@utility file-details-label {
  font-size: 14px;
  line-height: 20px;
  width: 30%;
  color: var(--light-100);
}

@utility file-details-value {
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
  flex: 1;
}

@utility share-wrapper {
  margin-top: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

@utility share-input-field {
  font-size: 14px;
  line-height: 20px;
  height: 52px;
  width: 100%;
  border-radius: 9999px;
  border: 1px solid;
  padding-left: 1rem;
  padding-right: 1rem;
  box-shadow: var(--drop-1);
  outline: none;
}

@utility share-remove-user {
  border-radius: 9999px;
  background-color: transparent;
  color: var(--light-100);
  box-shadow: none;
}

@utility share-remove-user {
  background-color: transparent;
}

@utility remove-icon {
  aspect-ratio: 1;
  border-radius: 9999px;
}

@utility auth-form {
  display: flex;
  max-height: 800px;
  width: 100%;
  max-width: 580px;
  flex-direction: column;
  justify-content: center;
  gap: 1.5rem;
  transition: all 0.3s;
}

@utility form-title {
  font-size: 34px;
  line-height: 42px;
  font-weight: 700;
  text-align: center;
  color: var(--light-100);
}

@utility form-submit-button {
  height: 66px;
  background-color: var(--color-brand);
  transition: all 0.3s;
  border-radius: 9999px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
}

@utility error-message {
  font-size: 14px;
  line-height: 20px;
  margin-left: auto;
  margin-right: auto;
  width: fit-content;
  border-radius: 0.75rem;
  background-color: rgba(239, 68, 68, 0.05);
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  text-align: center;
  color: var(--error);
}

@utility file-card {
  display: flex;
  cursor: pointer;
  flex-direction: column;
  gap: 1.5rem;
  border-radius: 1.125rem;
  background-color: white;
  padding: 1.25rem;
  transition: all 0.3s;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

@utility file-card {
  box-shadow: var(--drop-3);
}

@utility file-card-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  color: var(--light-100);
}

@utility chart {
  display: flex;
  align-items: center;
  border-radius: 1.25rem;
  background-color: var(--color-brand);
  padding: 1.25rem;
  color: white;
  flex-direction: column;
}

@utility chart-container {
  margin-left: auto;
  margin-right: auto;
  aspect-ratio: 1;
  width: 180px;
  color: white;
}

@utility polar-grid {
  fill: white;
  opacity: 0.2;
}

@utility chart-details {
  flex: 1;
  align-items: flex-start;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0;
  padding-bottom: 0;
}

@utility chart-total-percentage {
  fill: white;
  font-size: 2.25rem;
  font-weight: 700;
}

@utility chart-title {
  font-size: 20px;
  line-height: 28px;
  font-weight: 600;
  text-align: center;
}

@utility chart-description {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-top: 0.5rem;
  width: 100%;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

@utility uploader-button {
  height: 52px;
  gap: 0.5rem;
  padding-left: 2.5rem;
  padding-right: 2.5rem;
  background-color: var(--color-brand);
  transition: all 0.3s;
  border-radius: 9999px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  box-shadow: var(--drop-1);
}

@utility uploader-button {
  background-color: var(--color-brand-100);
}

@utility uploader-preview-list {
  position: fixed;
  bottom: 2.5rem;
  right: 2.5rem;
  z-index: 50;
  display: flex;
  height: fit-content;
  max-width: 480px;
  flex-direction: column;
  gap: 0.75rem;
  border-radius: 1.25rem;
  background-color: white;
  padding: 1.75rem;
  box-shadow: var(--drop-3);
}

@utility uploader-preview-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
  border-radius: 0.75rem;
  padding: 0.75rem;
  box-shadow: var(--drop-3);
}

@utility preview-item-name {
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
  margin-bottom: 0.5rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  max-width: 300px;
}

@utility error-toast {
  background-color: var(--red);
  border-radius: 0.625rem;
}

@utility header {
  display: none;
  align-items: center;
  justify-content: space-between;
  gap: 1.25rem;
  padding: 1.25rem;
}

@utility header-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: fit-content;
  gap: 1rem;
}

@utility header-user {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: 9999px;
  padding: 0.25rem;
  color: var(--light-100);
  background-color: rgba(var(--color-brand-rgb), 0.1);
}

@utility header-user-avatar {
  aspect-ratio: 1;
  width: 2.5rem;
  border-radius: 9999px;
  object-fit: cover;
}

@utility mobile-nav {
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  flex: 1;
  gap: 0.25rem;
  color: var(--color-brand);
}

@utility mobile-nav-list {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 1rem;
}

@utility mobile-nav-item {
  display: flex;
  color: var(--light-100);
  gap: 1rem;
  width: 100%;
  justify-content: flex-start;
  align-items: center;
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  height: 52px;
  border-radius: 9999px;
}

@utility mobile-sign-out-button {
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  display: flex;
  height: 52px;
  width: 100%;
  align-items: center;
  gap: 1rem;
  border-radius: 9999px;
  background-color: rgba(var(--color-brand-rgb), 0.1);
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  color: var(--color-brand);
  box-shadow: none;
  transition: all 0.3s;
}

@utility mobile-sign-out-button {
  background-color: rgba(var(--color-brand-rgb), 0.2);
}

@utility otp-close-button {
  position: absolute;
  right: -0.25rem;
  top: -0.75rem;
  cursor: pointer;
}

@utility search {
  position: relative;
  width: 100%;
  max-width: 480px;
}

@utility search-input-wrapper {
  display: flex;
  height: 52px;
  flex: 1;
  align-items: center;
  gap: 0.75rem;
  border-radius: 9999px;
  padding-left: 1rem;
  padding-right: 1rem;
  box-shadow: var(--drop-3);
}

@utility search-input {
  font-size: 14px;
  line-height: 20px;
  width: 100%;
  border: none;
  padding: 0;
  box-shadow: none;
  outline: none;
}

@utility search-input {
  font-size: 16px;
  line-height: 24px;
  color: var(--light-200);
}

@utility search-result {
  position: absolute;
  left: 0;
  top: 4rem;
  z-index: 50;
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 0.75rem;
  border-radius: 1.25rem;
  background-color: white;
  padding: 1rem;
}

@utility empty-result {
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  color: var(--light-100);
}

@utility sidebar {
  display: none;
  height: 100vh;
  width: 90px;
  flex-direction: column;
  overflow: auto;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 1.75rem;
  padding-bottom: 1.75rem;
}

@utility sidebar-nav {
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  margin-top: 2.25rem;
  flex: 1;
  gap: 0.25rem;
  color: var(--color-brand);
}

@utility sidebar-nav-item {
  display: flex;
  color: var(--light-100);
  gap: 1rem;
  border-radius: 0.75rem;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  height: 52px;
  padding-left: 1.875rem;
  border-radius: 9999px;
}

@utility sidebar-user-info {
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: 9999px;
  padding: 0.25rem;
  background-color: rgba(var(--color-brand-rgb), 0.1);
  color: var(--light-100);
}

@utility sidebar-user-avatar {
  aspect-ratio: 1;
  width: 2.5rem;
  border-radius: 9999px;
  object-fit: cover;
}

@utility shad-active {
  background-color: var(--color-brand);
  color: white;
  box-shadow: var(--drop-2);
}

@utility sort-select {
  height: 2.75rem;
  width: 100%;
  border-radius: 0.5rem;
  border-color: transparent;
  background-color: white;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  outline: none;
}

@utility sort-select-content {
  box-shadow: var(--drop-3);
}

@utility thumbnail {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  min-width: 50px;
  overflow: hidden;
  border-radius: 9999px;
  background-color: rgba(var(--color-brand-rgb), 0.1);
}

@utility thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Remove scrollbar */
.remove-scrollbar::-webkit-scrollbar {
  width: 0px;
  height: 0px;
  border-radius: 0px;
}
.remove-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}
.remove-scrollbar::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 0px;
}
.remove-scrollbar::-webkit-scrollbar-thumb {
  /* background: #1e2238; */
  background: transparent;
}
.recharts-responsive-container {
  height: initial !important;
}

/* Custom font family */
@font-face {
}

/* Dark theme */
@media (prefers-color-scheme: dark) {
  :root {
    --dark-100: #04050c;
    --dark-200: #131524;
  }
}

/* Apply custom styles, keyframes, and animations */
@keyframes caret-blink {
  0%,
  70%,
  100% {
    opacity: 1;
  }
  20%,
  50% {
    opacity: 0;
  }
}

/* Example of custom box shadows */
.shadow-drop-1 {
  box-shadow: 0px 10px 30px 0 rgba(66, 71, 97, 0.1);
}
.shadow-drop-2 {
  box-shadow: 0 8px 30px 0 rgba(65, 89, 214, 0.3);
}
.shadow-drop-3 {
  box-shadow: 0 8px 30px 0 rgba(65, 89, 214, 0.1);
}

/* Custom border radii */
.rounded-lg {
  border-radius: var(--radius);
}
.rounded-md {
  border-radius: calc(var(--radius) - 2px);
}
.rounded-sm {
  border-radius: calc(var(--radius) - 4px);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
