"use client";
import * as React from "react";
import { usePathname } from "next/navigation";
import {
  Sidebar,
  SidebarContent,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarHeader,
  SidebarFooter,
} from "@/components/ui/sidebar";
import { useSession } from "@/lib/auth-client";
import Link from "next/link";
import {
  LayoutDashboard,
  FileText,
  Video,
  Image as ImageIcon,
  Folder,
} from "lucide-react";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname();
  const session = useSession();
  const userId = session?.data?.user?.id;
  // Update navItems data with dynamic routes and icons
  const navItems = [
    {
      title: "Dashboard",
      url: `/user/${userId}/dashboard`,
      icon: <LayoutDashboard className="mr-2 h-5 w-5" />,
    },
    {
      title: "Documents",
      url: `/user/${userId}/documents`,
      icon: <FileText className="mr-2 h-5 w-5" />,
    },
    {
      title: "Images",
      url: `/user/${userId}/images`,
      icon: <ImageIcon className="mr-2 h-5 w-5" />,
    },
    {
      title: "Media",
      url: `/user/${userId}/media`,
      icon: <Video className="mr-2 h-5 w-5" />,
    },
    {
      title: "Others",
      url: `/user/${userId}/others`,
      icon: <Folder className="mr-2 h-5 w-5" />,
    },
  ];

  return (
    <Sidebar
      {...props}
      className="min-h-screen w-64 border-r border-slate-700 bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 shadow-2xl"
    >
      <SidebarHeader className="border-b border-slate-700 bg-slate-800/50 backdrop-blur-sm">
        <div className="flex items-center justify-center py-6">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg">
              <svg
                className="h-6 w-6 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"
                />
              </svg>
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">SafeSpace</h1>
              <p className="text-xs text-slate-400">File Storage</p>
            </div>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="px-4 py-6">
        <div className="mb-6">
          <h2 className="mb-3 text-xs font-semibold tracking-wider text-slate-400 uppercase">
            Navigation
          </h2>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-2">
              {navItems.map((item) => {
                const isActive = pathname.includes(item.title.toLowerCase());
                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      isActive={isActive}
                      className={`group relative flex items-center rounded-xl px-4 py-3 font-medium transition-all duration-200 ${
                        isActive
                          ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg shadow-blue-500/25"
                          : "text-slate-300 hover:bg-slate-700/50 hover:text-white"
                      }`}
                    >
                      <Link
                        href={item.url}
                        className="flex w-full items-center"
                      >
                        <div
                          className={`mr-3 transition-transform duration-200 ${isActive ? "scale-110" : "group-hover:scale-105"}`}
                        >
                          {React.cloneElement(item.icon, {
                            className: `h-5 w-5 ${isActive ? "text-white" : "text-slate-400 group-hover:text-white"}`,
                          })}
                        </div>
                        <span className="text-sm">{item.title}</span>
                        {isActive && (
                          <div className="absolute right-2 h-2 w-2 rounded-full bg-white shadow-sm"></div>
                        )}
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </div>

        {/* User Info Section */}
        <div className="rounded-xl border border-slate-700 bg-slate-800/50 p-4 backdrop-blur-sm">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-green-400 to-blue-500 text-sm font-bold text-white">
              {session?.data?.user?.name?.charAt(0) || "U"}
            </div>
            <div className="min-w-0 flex-1">
              <p className="truncate text-sm font-medium text-white">
                {session?.data?.user?.name || "User"}
              </p>
              <p className="truncate text-xs text-slate-400">
                {session?.data?.user?.email || "<EMAIL>"}
              </p>
            </div>
          </div>
        </div>
      </SidebarContent>

      <SidebarRail />

      <SidebarFooter className="border-t border-slate-700 bg-slate-800/30 p-4">
        <div className="text-center">
          <div className="mb-3 text-xs text-slate-400">
            © {new Date().getFullYear()} SafeSpace
          </div>
          <div className="text-xs text-slate-500">Made with ❤️ by Ayman</div>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
