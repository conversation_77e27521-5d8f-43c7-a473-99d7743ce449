"use client";
import * as React from "react";
import { usePathname } from "next/navigation";
import {
  Sidebar,
  SidebarContent,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarHeader,
  SidebarFooter,
} from "@/components/ui/sidebar";
import { useSession } from "@/lib/auth-client";
import Link from "next/link";
import {
  LayoutDashboard,
  FileText,
  Video,
  Image as ImageIcon,
  Folder,
} from "lucide-react";
import Logo from "@/components/logo";
import Image from "next/image";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname();
  const session = useSession();
  const userId = session?.data?.user?.id;
  // Update navItems data with dynamic routes and icons
  const navItems = [
    {
      title: "Dashboard",
      url: `/user/${userId}/dashboard`,
      icon: <LayoutDashboard className="mr-2 h-5 w-5" />,
    },
    {
      title: "Documents",
      url: `/user/${userId}/documents`,
      icon: <FileText className="mr-2 h-5 w-5" />,
    },
    {
      title: "Images",
      url: `/user/${userId}/images`,
      icon: <ImageIcon className="mr-2 h-5 w-5" />,
    },
    {
      title: "Media",
      url: `/user/${userId}/media`,
      icon: <Video className="mr-2 h-5 w-5" />,
    },
    {
      title: "Others",
      url: `/user/${userId}/others`,
      icon: <Folder className="mr-2 h-5 w-5" />,
    },
  ];

  return (
    <Sidebar
      {...props}
      className="from-brand/10 min-h-screen w-64 to-white shadow-lg"
    >
      <SidebarHeader>
        <div className="flex items-center">
          <a href="#" className="text-brand hover:text-brand/90">
            <Logo />
          </a>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroupContent>
          <SidebarMenu>
            {navItems.map((item) => {
              const isActive = pathname.includes(item.title.toLowerCase());
              return (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={isActive}
                    className={`my-1 flex items-center rounded-lg px-4 py-2 font-medium transition-colors duration-150 ${
                      isActive
                        ? "shad-active text-white shadow"
                        : "hover:bg-brand/5 hover:text-brand text-gray-700"
                    }`}
                  >
                    <Link href={item.url} className="flex w-full items-center">
                      {item.icon}
                      {item.title}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              );
            })}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarContent>
      <SidebarRail />
      <SidebarFooter>
        <Image
          className="w-full"
          src="/assets/images/files-2.png"
          alt="Logo"
          width={506}
          height={418}
        />
        <div className="mt-4 text-center text-sm text-gray-500">
          © {new Date().getFullYear()} SafeSpace
        </div>
        <div className="mt-2 text-center text-xs text-gray-400">
          All rights reserved.
        </div>
        <div className="mt-2 text-center text-xs text-gray-400">
          Version 1.0.0
        </div>
        <div className="mt-2 text-center text-xs text-gray-400">
          Made with ❤️ by Ayman
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
