import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import Link from "next/link";

const Page = async () => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session) {
    redirect("/sign-in");
  }

  const userId = session.user.id;

  return (
    <div className="from-background to-background/80 min-h-screen bg-gradient-to-b">
      <div className="container mx-auto px-4 py-16">
        <div className="bg-card mx-auto max-w-3xl rounded-2xl p-8 shadow-lg">
          <div className="mb-8 flex items-center gap-4">
            <div className="bg-primary text-primary-foreground flex h-16 w-16 items-center justify-center rounded-full text-2xl font-bold">
              {session.user.name.charAt(0)}
            </div>

            <div>
              <h1 className="text-foreground text-3xl font-bold">
                Welcome, {session.user.name}!
              </h1>
              <p className="text-muted-foreground">
                We&apos;re glad to see you here
              </p>
            </div>
          </div>

          <div className="mb-8">
            <h2 className="text-foreground mb-4 text-xl font-semibold">
              Quick Access
            </h2>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <Link href={`/user/${userId}/dashboard`}>
                <div className="group border-border bg-card hover:border-primary flex items-center gap-3 rounded-lg border p-4 transition-all hover:shadow-md">
                  <div className="bg-primary/10 text-primary group-hover:bg-primary group-hover:text-primary-foreground rounded-full p-3">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect width="7" height="9" x="3" y="3" rx="1" />
                      <rect width="7" height="5" x="14" y="3" rx="1" />
                      <rect width="7" height="9" x="14" y="12" rx="1" />
                      <rect width="7" height="5" x="3" y="16" rx="1" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-foreground font-medium">Dashboard</h3>
                    <p className="text-muted-foreground text-sm">
                      View your activity and stats
                    </p>
                  </div>
                </div>
              </Link>

              <Link href={`/user/${userId}/documents`}>
                <div className="group border-border bg-card hover:border-primary flex items-center gap-3 rounded-lg border p-4 transition-all hover:shadow-md">
                  <div className="bg-primary/10 text-primary group-hover:bg-primary group-hover:text-primary-foreground rounded-full p-3">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                      <polyline points="14 2 14 8 20 8" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-foreground font-medium">Documents</h3>
                    <p className="text-muted-foreground text-sm">
                      Manage your files
                    </p>
                  </div>
                </div>
              </Link>
            </div>
          </div>

          <div className="flex justify-center">
            <Link href={`/user/${userId}/dashboard`}>
              <button className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-full px-6 py-3 font-medium transition-colors">
                Go to Dashboard
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Page;
