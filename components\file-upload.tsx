"use client";
import { useState, useCallback } from "react";
import { uploadFile } from "@/app/actions/userActions";
import { toast } from "sonner";
import { useDropzone } from "react-dropzone";
import { useSession } from "@/lib/auth-client";
import { fileType } from "@/app/types";
import { getFileType } from "@/lib/utils";

const FileUpload = ({ type }: { type: fileType }) => {
  const [files, setFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);

  const userId = useSession()?.data?.user.id;

  const handleUpload = useCallback(async () => {
    if (files.length === 0) {
      toast.error("Please select at least one file to upload");
      return;
    }
    setUploading(true);
    try {
      for (const file of files) {
        const maxSizeMB = 5;
        if (file.size > maxSizeMB * 1024 * 1024) {
          toast.error(`File "${file.name}" exceeds ${maxSizeMB}MB limit`);
          continue;
        }
        const type = getFileType(file.name);
        const filePath = `uploads/user/${userId}/${type}/${file.name}`;
        await uploadFile(file, filePath, userId);
      }
      toast.success("Files uploaded successfully");
      setFiles([]);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      toast.error(`Upload failed: ${errorMessage}`);
    } finally {
      setUploading(false);
    }
  }, [files, userId]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setFiles(acceptedFiles);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: true,
  });

  return (
    <div
      {...getRootProps()}
      className={`flex w-full max-w-md cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed bg-white p-8 shadow-md transition-colors duration-200 ${
        isDragActive ? "border-brand bg-brand/10" : "border-gray-300"
      }`}
    >
      <input {...getInputProps()} multiple />
      <div className="flex flex-col items-center gap-2">
        <svg
          className="text-brand mb-2 h-10 w-10"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M4 16v2a2 2 0 002 2h12a2 2 0 002-2v-2M7 10l5-5m0 0l5 5m-5-5v12"
          />
        </svg>
        {isDragActive ? (
          <p className="text-brand font-semibold">Drop the files here ...</p>
        ) : (
          <p className="text-gray-500">
            Drag &apos;n&apos; drop some files here, or click to select files
          </p>
        )}
        {files.length > 0 && (
          <div className="mt-4 flex w-full flex-col items-center">
            <ul className="mb-2 w-full max-w-xs text-sm text-gray-700">
              {files.map((file) => (
                <li key={file.name} className="truncate">
                  {file.name}
                </li>
              ))}
            </ul>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleUpload();
              }}
              disabled={uploading}
              className="bg-brand hover:bg-brand/90 mt-2 w-full rounded px-4 py-2 font-semibold text-white shadow transition disabled:opacity-50"
            >
              {uploading ? "Uploading..." : "Upload"}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default FileUpload;
