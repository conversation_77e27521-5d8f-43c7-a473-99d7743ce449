import DashboardCard from "@/components/dashboard-card";
import ListRecentFiles from "@/components/list-recent-files";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { listFiles } from "@/app/actions/userActions";
import { StorageChart } from "@/components/storage-chart";

const page = async () => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });
  const userId = session?.user?.id;
  const files = await listFiles(userId);
  return (
    <main className="container flex flex-col justify-between gap-10 lg:flex-row">
      <section className="ml-0 flex-1 lg:ml-5">
        <StorageChart files={files} />
        <div className="mt-10 grid grid-cols-2 grid-rows-2 gap-4">
          <DashboardCard type="images" files={files} />
          <DashboardCard type="videos" files={files} />
          <DashboardCard type="documents" files={files} />
          <DashboardCard type="others" files={files} />
        </div>
      </section>
      <section className="mt-5 rounded-lg bg-white p-4 lg:w-[500px]">
        <h2 className="mb-6 text-lg font-bold">Recent Uploads</h2>
        <ListRecentFiles files={files} />
      </section>
    </main>
  );
};

export default page;
